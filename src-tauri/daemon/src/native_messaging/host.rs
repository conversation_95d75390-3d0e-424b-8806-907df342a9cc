//! # Native Messaging Host 实现
//! 
//! 实现了标准的 Native Messaging Host，负责与浏览器扩展通信，
//! 并将请求代理到守护进程的主服务。

use std::collections::HashMap;
use std::io::{self, BufReader, BufWriter};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{mpsc, RwLock};
use tokio::time::timeout;
use tracing::{debug, error, info, warn};
use serde::{Deserialize, Serialize};

use crate::ipc::{IpcClient, IpcMessage, IpcResponse, ClientConfig as IpcClientConfig};
use crate::native_messaging::{
    Result, NativeMessagingError, DEFAULT_REQUEST_TIMEOUT,
    protocol::{ProtocolCodec, ProtocolVersion, NativeMessage, MessageType},
    proxy::{RequestProxy, ProxyConfig},
};

/// Native Messaging Host 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HostConfig {
    /// Host 名称
    pub host_name: String,
    /// Host 描述
    pub description: String,
    /// 可执行文件路径
    pub executable_path: String,
    /// 允许的扩展来源
    pub allowed_origins: Vec<String>,
    /// 允许的扩展ID
    pub allowed_extensions: Vec<String>,
    /// IPC 客户端配置
    pub ipc_config: IpcClientConfig,
    /// 协议版本
    pub protocol_version: ProtocolVersion,
    /// 请求超时时间
    pub request_timeout: Duration,
    /// 最大并发请求数
    pub max_concurrent_requests: usize,
    /// 是否启用调试模式
    pub debug_mode: bool,
    /// 心跳间隔
    pub heartbeat_interval: Duration,
    /// 是否启用消息签名验证
    pub verify_signatures: bool,
    /// 消息签名密钥
    pub signature_key: Option<String>,
}

impl Default for HostConfig {
    fn default() -> Self {
        Self {
            host_name: "com.securepassword.host".to_string(),
            description: "Secure Password Native Messaging Host".to_string(),
            executable_path: std::env::current_exe()
                .unwrap_or_else(|_| "secure-password-daemon".into())
                .to_string_lossy()
                .to_string(),
            allowed_origins: Self::get_default_allowed_origins(),
            allowed_extensions: Self::get_default_allowed_extensions(),
            ipc_config: IpcClientConfig::default(),
            protocol_version: ProtocolVersion::V2,
            request_timeout: DEFAULT_REQUEST_TIMEOUT,
            max_concurrent_requests: 100,
            debug_mode: false,
            heartbeat_interval: Duration::from_secs(30),
            verify_signatures: false,
            signature_key: None,
        }
    }
}

impl HostConfig {
    /// 获取默认允许的来源列表
    fn get_default_allowed_origins() -> Vec<String> {
        vec![
            // Chrome 扩展
            "chrome-extension://secure-password-extension/".to_string(),
            // Firefox 扩展
            "moz-extension://secure-password-extension/".to_string(),
            // Edge 扩展
            "ms-browser-extension://secure-password-extension/".to_string(),
        ]
    }

    /// 获取默认允许的扩展ID列表
    fn get_default_allowed_extensions() -> Vec<String> {
        vec![
            // 生产环境扩展ID (需要在发布时替换为实际ID)
            "secure-password-extension".to_string(),
        ]
    }

    /// 从环境变量或配置文件加载允许的扩展
    pub fn load_allowed_extensions_from_config() -> Vec<String> {
        // 尝试从环境变量读取
        if let Ok(extensions_str) = std::env::var("SECURE_PASSWORD_ALLOWED_EXTENSIONS") {
            return extensions_str
                .split(',')
                .map(|s| s.trim().to_string())
                .filter(|s| !s.is_empty())
                .collect();
        }

        // 尝试从配置文件读取
        if let Ok(config_path) = std::env::var("SECURE_PASSWORD_CONFIG_PATH") {
            if let Ok(config_content) = std::fs::read_to_string(config_path) {
                if let Ok(config_json) = serde_json::from_str::<serde_json::Value>(&config_content) {
                    if let Some(extensions) = config_json.get("allowed_extensions").and_then(|v| v.as_array()) {
                        return extensions
                            .iter()
                            .filter_map(|v| v.as_str().map(|s| s.to_string()))
                            .collect();
                    }
                }
            }
        }

        // 返回默认值
        Self::get_default_allowed_extensions()
    }
}

/// Host 运行状态
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum HostState {
    /// 未启动
    Stopped,
    /// 启动中
    Starting,
    /// 运行中
    Running,
    /// 停止中
    Stopping,
    /// 错误状态
    Error(String),
}

/// Host 统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HostStats {
    /// 启动时间
    pub started_at: Option<std::time::SystemTime>,
    /// 处理的请求总数
    pub total_requests: u64,
    /// 成功处理的请求数
    pub successful_requests: u64,
    /// 失败的请求数
    pub failed_requests: u64,
    /// 当前并发请求数
    pub concurrent_requests: u32,
    /// 平均响应时间 (毫秒)
    pub avg_response_time_ms: f64,
    /// 最后活动时间
    pub last_activity: Option<std::time::SystemTime>,
    /// IPC 连接状态
    pub ipc_connected: bool,
}

impl Default for HostStats {
    fn default() -> Self {
        Self {
            started_at: None,
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            concurrent_requests: 0,
            avg_response_time_ms: 0.0,
            last_activity: None,
            ipc_connected: false,
        }
    }
}

/// 待处理请求信息
struct PendingRequest {
    /// 请求开始时间
    started_at: Instant,
    /// 响应发送器
    response_sender: mpsc::UnboundedSender<NativeMessage>,
}

/// Native Messaging Host 主实现
pub struct NativeMessagingHost {
    /// 配置
    config: HostConfig,
    /// 当前状态
    state: Arc<RwLock<HostState>>,
    /// 统计信息
    stats: Arc<RwLock<HostStats>>,
    /// 协议编解码器
    codec: ProtocolCodec,
    /// IPC 客户端
    ipc_client: Option<IpcClient>,
    /// 请求代理器
    request_proxy: Option<RequestProxy>,
    /// 待处理请求
    pending_requests: Arc<RwLock<HashMap<String, PendingRequest>>>,
    /// 关闭信号发送器
    shutdown_sender: Option<mpsc::UnboundedSender<()>>,
}

impl NativeMessagingHost {
    /// 创建新的 Native Messaging Host
    pub async fn new(config: HostConfig) -> Result<Self> {
        let codec = ProtocolCodec::new(config.protocol_version);
        
        Ok(Self {
            config,
            state: Arc::new(RwLock::new(HostState::Stopped)),
            stats: Arc::new(RwLock::new(HostStats::default())),
            codec,
            ipc_client: None,
            request_proxy: None,
            pending_requests: Arc::new(RwLock::new(HashMap::new())),
            shutdown_sender: None,
        })
    }
    
    /// 启动 Native Messaging Host
    pub async fn start(&mut self) -> Result<()> {
        info!("启动 Native Messaging Host: {}", self.config.host_name);
        
        // 更新状态
        *self.state.write().await = HostState::Starting;
        
        // 初始化IPC客户端
        self.initialize_ipc_client().await?;
        
        // 初始化请求代理器
        self.initialize_request_proxy().await?;
        
        // 启动后台任务
        self.start_background_tasks().await?;
        
        // 启动主消息循环
        self.start_message_loop().await?;
        
        Ok(())
    }
    
    /// 停止 Native Messaging Host
    pub async fn stop(&mut self) -> Result<()> {
        info!("停止 Native Messaging Host");
        
        // 更新状态
        *self.state.write().await = HostState::Stopping;
        
        // 发送关闭信号
        if let Some(sender) = &self.shutdown_sender {
            let _ = sender.send(());
        }
        
        // 断开IPC连接
        if let Some(mut client) = self.ipc_client.take() {
            if let Err(e) = client.disconnect().await {
                warn!("断开IPC连接时出错: {}", e);
            }
        }
        
        // 更新状态
        *self.state.write().await = HostState::Stopped;
        
        info!("Native Messaging Host 已停止");
        Ok(())
    }
    
    /// 获取当前状态
    pub async fn get_state(&self) -> HostState {
        self.state.read().await.clone()
    }
    
    /// 获取统计信息
    pub async fn get_stats(&self) -> HostStats {
        self.stats.read().await.clone()
    }
    
    /// 初始化IPC客户端
    async fn initialize_ipc_client(&mut self) -> Result<()> {
        info!("初始化IPC客户端连接");
        
        let mut client = IpcClient::new(self.config.ipc_config.clone());
        
        // 尝试连接，但不要求立即成功
        match client.connect().await {
            Ok(()) => {
                info!("IPC客户端连接成功");
                self.ipc_client = Some(client);
                self.stats.write().await.ipc_connected = true;
            }
            Err(e) => {
                warn!("IPC客户端连接失败，将在后台重试: {}", e);
                self.ipc_client = Some(client);
                self.stats.write().await.ipc_connected = false;
                // 不返回错误，允许Native Messaging Host继续启动
            }
        }
        
        Ok(())
    }
    
    /// 初始化请求代理器
    async fn initialize_request_proxy(&mut self) -> Result<()> {
        debug!("初始化请求代理器");
        
        let proxy_config = ProxyConfig {
            timeout: self.config.request_timeout,
            max_retries: 3,
            retry_delay: Duration::from_millis(500),
            enable_batch_processing: false,
            batch_size: 10,
            batch_timeout: Duration::from_millis(100),
            enable_caching: false,
            cache_ttl: Duration::from_secs(300),
        };
        
        if let Some(ipc_client) = &self.ipc_client {
            self.request_proxy = Some(RequestProxy::new(proxy_config, ipc_client.clone()).await?);
        } else {
            return Err(NativeMessagingError::InternalError(
                "IPC客户端未初始化".to_string()
            ));
        }
        
        info!("请求代理器初始化成功");
        Ok(())
    }
    
    /// 启动后台任务
    async fn start_background_tasks(&mut self) -> Result<()> {
        // 创建关闭信号通道
        let (shutdown_tx, shutdown_rx) = mpsc::unbounded_channel();
        let (shutdown_tx2, shutdown_rx2) = mpsc::unbounded_channel();
        self.shutdown_sender = Some(shutdown_tx);
        
        // 启动心跳任务
        self.start_heartbeat_task().await?;
        
        // 启动统计更新任务
        self.start_stats_update_task(shutdown_rx).await?;
        
        // 启动请求超时检查任务
        self.start_timeout_checker_task(shutdown_rx2).await?;
        
        Ok(())
    }
    
    /// 启动主消息循环
    async fn start_message_loop(&mut self) -> Result<()> {
        info!("启动消息循环，等待浏览器扩展连接");

        // 更新状态和统计信息
        *self.state.write().await = HostState::Running;
        self.stats.write().await.started_at = Some(std::time::SystemTime::now());

        // 创建异步标准输入输出的读写器
        let mut stdin = tokio::io::stdin();
        let mut stdout = tokio::io::stdout();
        
        // 添加超时机制
        let timeout_duration = std::time::Duration::from_secs(60); // 60秒超时
        
        loop {
            // 检查是否应该停止
            if matches!(*self.state.read().await, HostState::Stopping) {
                break;
            }
            
            // 使用超时读取消息
            let read_result = tokio::time::timeout(
                timeout_duration,
                self.read_message_from_browser_async(&mut stdin)
            ).await;
            
            match read_result {
                Ok(Ok(message)) => {
                    info!("收到浏览器消息: {:?}", message);
                    // 处理消息
                    if let Err(e) = self.handle_browser_message_async(message, &mut stdout).await {
                        error!("处理浏览器消息时出错: {}", e);
                        self.increment_failed_requests().await;
                    }
                }
                Ok(Err(NativeMessagingError::IoError(e))) if e.contains("UnexpectedEof") => {
                    // 标准输入关闭，正常退出
                    info!("标准输入关闭，退出消息循环");
                    break;
                }
                Ok(Err(e)) => {
                    error!("读取浏览器消息时出错: {}", e);
                    self.increment_failed_requests().await;

                    // 发送错误响应
                    let error_message = NativeMessage::new_error(
                        "unknown".to_string(),
                        "read_error".to_string(),
                        e.to_string(),
                        "host".to_string(),
                    );

                    if let Err(e) = self.send_message_to_browser_async(&error_message, &mut stdout).await {
                        error!("发送错误响应失败: {}", e);
                    }
                }
                Err(_timeout) => {
                    // 超时，检查是否有心跳或继续等待
                    debug!("读取消息超时，继续等待...");
                    continue;
                }
            }
        }
        
        Ok(())
    }
    
    /// 从浏览器读取消息
    async fn read_message_from_browser(&self, reader: &mut BufReader<io::Stdin>) -> Result<NativeMessage> {
        // 同步读取消息
        let message = self.codec.read_message(reader)?;
        debug!("从浏览器接收到消息: {:?}", message);
        Ok(message)
    }

    /// 从浏览器读取消息（异步版本）
    async fn read_message_from_browser_async(&self, reader: &mut tokio::io::Stdin) -> Result<NativeMessage> {
        // 异步读取消息
        let message = self.codec.read_message_async(reader).await?;
        debug!("从浏览器接收到消息: {:?}", message);
        Ok(message)
    }

    /// 向浏览器发送消息
    async fn send_message_to_browser(&self, message: &NativeMessage, writer: &mut BufWriter<io::Stdout>) -> Result<()> {
        debug!("向浏览器发送消息: {:?}", message);
        self.codec.write_message(writer, message)?;
        Ok(())
    }

    /// 向浏览器发送消息（异步版本）
    async fn send_message_to_browser_async(&self, message: &NativeMessage, writer: &mut tokio::io::Stdout) -> Result<()> {
        debug!("向浏览器发送消息: {:?}", message);
        self.codec.write_message_async(writer, message).await?;
        Ok(())
    }
    
    /// 处理浏览器消息
    async fn handle_browser_message(&mut self, message: NativeMessage, writer: &mut BufWriter<io::Stdout>) -> Result<()> {
        let request_start = Instant::now();
        
        // 更新统计信息
        self.increment_total_requests().await;
        self.increment_concurrent_requests().await;
        self.update_last_activity().await;
        
        // 验证消息
        if let Err(e) = self.validate_browser_message(&message).await {
            error!("消息验证失败: {}", e);
            
            let error_response = NativeMessage::new_error(
                message.request_id.clone(),
                "validation_error".to_string(),
                e.to_string(),
                "host".to_string(),
            );
            
            self.send_message_to_browser(&error_response, writer).await?;
            self.decrement_concurrent_requests().await;
            return Ok(());
        }
        
        // 处理不同类型的消息
        let response = match message.get_message_type() {
            MessageType::Ping => self.handle_ping_message(&message).await,
            MessageType::Request => self.handle_request_message(&message).await,
            _ => {
                warn!("收到不支持的消息类型: {}", message.message_type);
                Ok(NativeMessage::new_error(
                    message.request_id.clone(),
                    "unsupported_message_type".to_string(),
                    format!("不支持的消息类型: {}", message.message_type),
                    "host".to_string(),
                ))
            }
        };
        
        // 发送响应
        match response {
            Ok(response_message) => {
                self.send_message_to_browser(&response_message, writer).await?;
                self.increment_successful_requests().await;
            }
            Err(e) => {
                error!("处理消息时出错: {}", e);
                
                let error_response = NativeMessage::new_error(
                    message.request_id.clone(),
                    "processing_error".to_string(),
                    e.to_string(),
                    "host".to_string(),
                );
                
                self.send_message_to_browser(&error_response, writer).await?;
                self.increment_failed_requests().await;
            }
        }
        
        // 更新统计信息
        self.decrement_concurrent_requests().await;
        self.update_response_time_stats(request_start.elapsed()).await;
        
        Ok(())
    }

    /// 处理浏览器消息（异步版本）
    async fn handle_browser_message_async(&mut self, message: NativeMessage, writer: &mut tokio::io::Stdout) -> Result<()> {
        let request_start = Instant::now();

        // 更新统计信息
        self.increment_total_requests().await;
        self.increment_concurrent_requests().await;
        self.update_last_activity().await;

        // 验证消息
        if let Err(e) = self.validate_browser_message(&message).await {
            error!("消息验证失败: {}", e);

            let error_response = NativeMessage::new_error(
                message.request_id.clone(),
                "validation_error".to_string(),
                e.to_string(),
                "host".to_string(),
            );

            self.send_message_to_browser_async(&error_response, writer).await?;
            self.decrement_concurrent_requests().await;
            return Ok(());
        }

        // 处理不同类型的消息
        let response = match message.get_message_type() {
            MessageType::Ping => self.handle_ping_message(&message).await,
            MessageType::Request => self.handle_request_message(&message).await,
            _ => {
                warn!("收到不支持的消息类型: {}", message.message_type);
                Ok(NativeMessage::new_error(
                    message.request_id.clone(),
                    "unsupported_message_type".to_string(),
                    format!("不支持的消息类型: {}", message.message_type),
                    "host".to_string(),
                ))
            }
        };

        // 发送响应
        match response {
            Ok(response_message) => {
                self.send_message_to_browser_async(&response_message, writer).await?;
                self.increment_successful_requests().await;
            }
            Err(e) => {
                error!("处理消息时出错: {}", e);

                let error_response = NativeMessage::new_error(
                    message.request_id.clone(),
                    "processing_error".to_string(),
                    e.to_string(),
                    "host".to_string(),
                );

                self.send_message_to_browser_async(&error_response, writer).await?;
                self.increment_failed_requests().await;
            }
        }

        // 更新统计信息
        self.decrement_concurrent_requests().await;
        self.update_response_time_stats(request_start.elapsed()).await;

        Ok(())
    }

    /// 验证浏览器消息
    async fn validate_browser_message(&self, message: &NativeMessage) -> Result<()> {
        // 基本格式验证
        message.validate()?;
        
        // 验证扩展来源
        if !self.config.allowed_origins.is_empty() {
            // 这里应该检查消息的来源，但在标准Native Messaging中
            // 来源信息通常不在消息中，而是通过其他方式验证
            debug!("跳过来源验证 (标准Native Messaging协议限制)");
        }
        
        // 验证扩展ID (如果在消息中提供)
        if let Some(extension_id) = message.get_extension("extension_id") {
            if let Some(id_str) = extension_id.as_str() {
                if !self.config.allowed_extensions.contains(&id_str.to_string()) {
                    return Err(NativeMessagingError::ExtensionNotAuthorized(id_str.to_string()));
                }
            }
        }
        
        // 验证消息签名 (如果启用)
        if self.config.verify_signatures {
            if message.signature.is_none() {
                return Err(NativeMessagingError::SecurityError(
                    "消息缺少必需的签名".to_string()
                ));
            }

            // 实现签名验证逻辑
            if let Some(signature) = &message.signature {
                if !self.verify_message_signature(message, signature).await? {
                    return Err(NativeMessagingError::SecurityError(
                        "消息签名验证失败".to_string()
                    ));
                }
                debug!("消息签名验证成功: {}", message.request_id);
            }
        }
        
        Ok(())
    }
    
    /// 处理心跳消息
    async fn handle_ping_message(&self, message: &NativeMessage) -> Result<NativeMessage> {
        debug!("处理心跳消息: {}", message.request_id);
        
        // 创建简单的pong响应
        let mut pong = NativeMessage::new_pong(message.request_id.clone(), "host".to_string());
        
        // 复制一些扩展字段
        if let Some(extension_id) = message.get_extension("extension_id") {
            pong.add_extension("extension_id".to_string(), extension_id.clone());
        }
        
        info!("发送心跳响应: {}", pong.request_id);
        Ok(pong)
    }
    
    /// 验证消息签名
    async fn verify_message_signature(&self, message: &NativeMessage, signature: &str) -> Result<bool> {
        // 构建待验证的消息内容
        let message_content = format!(
            "{}:{}:{}:{}",
            message.version,
            message.message_type,
            message.request_id,
            serde_json::to_string(&message.payload)?
        );

        // 从配置中获取签名密钥 (实际应用中应该从安全存储中获取)
        let secret_key = self.config.signature_key.as_deref().unwrap_or("default_secret_key");

        // 使用简单的哈希验证 (生产环境应使用 HMAC-SHA256)
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        message_content.hash(&mut hasher);
        secret_key.hash(&mut hasher);
        let expected_signature = format!("{:x}", hasher.finish());

        // 比较签名 (使用常量时间比较防止时序攻击)
        Ok(self.constant_time_compare(signature, &expected_signature))
    }

    /// 常量时间字符串比较，防止时序攻击
    fn constant_time_compare(&self, a: &str, b: &str) -> bool {
        if a.len() != b.len() {
            return false;
        }

        let mut result = 0u8;
        for (byte_a, byte_b) in a.bytes().zip(b.bytes()) {
            result |= byte_a ^ byte_b;
        }
        result == 0
    }

    /// 处理请求消息
    async fn handle_request_message(&mut self, message: &NativeMessage) -> Result<NativeMessage> {
        debug!("处理请求消息: {}", message.request_id);
        
        // 检查并发请求限制
        let concurrent_count = self.stats.read().await.concurrent_requests;
        if concurrent_count >= self.config.max_concurrent_requests as u32 {
            return Ok(NativeMessage::new_error(
                message.request_id.clone(),
                "too_many_requests".to_string(),
                "并发请求数超过限制".to_string(),
                "host".to_string(),
            ));
        }
        
        // 通过代理转发请求到守护进程
        if let Some(proxy) = &self.request_proxy {
            match timeout(self.config.request_timeout, proxy.proxy_request(message.clone())).await {
                Ok(Ok(response)) => Ok(response),
                Ok(Err(e)) => {
                    error!("代理请求失败: {}", e);
                    Ok(NativeMessage::new_error(
                        message.request_id.clone(),
                        "proxy_error".to_string(),
                        e.to_string(),
                        "host".to_string(),
                    ))
                }
                Err(_) => {
                    error!("请求超时: {}", message.request_id);
                    Ok(NativeMessage::new_error(
                        message.request_id.clone(),
                        "timeout".to_string(),
                        "请求处理超时".to_string(),
                        "host".to_string(),
                    ))
                }
            }
        } else {
            Err(NativeMessagingError::InternalError(
                "请求代理器未初始化".to_string()
            ))
        }
    }
    
    /// 启动心跳任务
    async fn start_heartbeat_task(&self) -> Result<()> {
        let ipc_client = self.ipc_client.as_ref().unwrap().clone();
        let interval = self.config.heartbeat_interval;
        let stats = self.stats.clone();
        
        tokio::spawn(async move {
            let mut heartbeat_interval = tokio::time::interval(interval);
            
            loop {
                heartbeat_interval.tick().await;
                
                // 发送心跳到IPC服务器
                match ipc_client.ping().await {
                    Ok(_) => {
                        debug!("IPC心跳成功");
                        stats.write().await.ipc_connected = true;
                    }
                    Err(e) => {
                        warn!("IPC心跳失败: {}", e);
                        stats.write().await.ipc_connected = false;
                    }
                }
            }
        });
        
        Ok(())
    }
    
    /// 启动统计更新任务
    async fn start_stats_update_task(&self, mut shutdown_receiver: mpsc::UnboundedReceiver<()>) -> Result<()> {
        let stats = self.stats.clone();
        
        tokio::spawn(async move {
            let mut stats_interval = tokio::time::interval(Duration::from_secs(60));
            
            loop {
                tokio::select! {
                    _ = stats_interval.tick() => {
                        // 定期输出统计信息
                        let current_stats = stats.read().await;
                        info!("Host统计: 总请求={}, 成功={}, 失败={}, 并发={}", 
                              current_stats.total_requests,
                              current_stats.successful_requests,
                              current_stats.failed_requests,
                              current_stats.concurrent_requests);
                    }
                    _ = shutdown_receiver.recv() => {
                        debug!("统计更新任务收到关闭信号");
                        break;
                    }
                }
            }
        });
        
        Ok(())
    }
    
    /// 启动超时检查任务
    async fn start_timeout_checker_task(&self, mut shutdown_receiver: mpsc::UnboundedReceiver<()>) -> Result<()> {
        let pending_requests = self.pending_requests.clone();
        let timeout_duration = self.config.request_timeout;
        
        tokio::spawn(async move {
            let mut timeout_interval = tokio::time::interval(Duration::from_secs(10));
            
            loop {
                tokio::select! {
                    _ = timeout_interval.tick() => {
                        // 检查超时的请求
                        let now = Instant::now();
                        let mut requests_to_remove = Vec::new();
                        
                        {
                            let requests = pending_requests.read().await;
                            for (request_id, pending) in requests.iter() {
                                if now.duration_since(pending.started_at) > timeout_duration {
                                    requests_to_remove.push(request_id.clone());
                                }
                            }
                        }
                        
                        // 移除超时的请求
                        if !requests_to_remove.is_empty() {
                            let mut requests = pending_requests.write().await;
                            for request_id in requests_to_remove {
                                if let Some(pending) = requests.remove(&request_id) {
                                    warn!("请求超时: {}", request_id);
                                    
                                    // 发送超时错误响应
                                    let timeout_response = NativeMessage::new_error(
                                        request_id,
                                        "timeout".to_string(),
                                        "请求处理超时".to_string(),
                                        "host".to_string(),
                                    );
                                    
                                    let _ = pending.response_sender.send(timeout_response);
                                }
                            }
                        }
                    }
                    _ = shutdown_receiver.recv() => {
                        debug!("超时检查任务收到关闭信号");
                        break;
                    }
                }
            }
        });
        
        Ok(())
    }
    
    // 统计信息更新方法
    async fn increment_total_requests(&self) {
        self.stats.write().await.total_requests += 1;
    }
    
    async fn increment_successful_requests(&self) {
        self.stats.write().await.successful_requests += 1;
    }
    
    async fn increment_failed_requests(&self) {
        self.stats.write().await.failed_requests += 1;
    }
    
    async fn increment_concurrent_requests(&self) {
        self.stats.write().await.concurrent_requests += 1;
    }
    
    async fn decrement_concurrent_requests(&self) {
        let mut stats = self.stats.write().await;
        if stats.concurrent_requests > 0 {
            stats.concurrent_requests -= 1;
        }
    }
    
    async fn update_last_activity(&self) {
        self.stats.write().await.last_activity = Some(std::time::SystemTime::now());
    }
    
    async fn update_response_time_stats(&self, response_time: Duration) {
        let mut stats = self.stats.write().await;
        let response_time_ms = response_time.as_millis() as f64;
        
        // 简单的移动平均
        if stats.avg_response_time_ms == 0.0 {
            stats.avg_response_time_ms = response_time_ms;
        } else {
            stats.avg_response_time_ms = (stats.avg_response_time_ms * 0.9) + (response_time_ms * 0.1);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_host_creation() {
        let config = HostConfig::default();
        let host = NativeMessagingHost::new(config).await.unwrap();
        
        assert_eq!(host.get_state().await, HostState::Stopped);
        
        let stats = host.get_stats().await;
        assert_eq!(stats.total_requests, 0);
        assert_eq!(stats.successful_requests, 0);
        assert_eq!(stats.failed_requests, 0);
    }

    #[tokio::test]
    async fn test_host_config_default() {
        let config = HostConfig::default();
        
        assert_eq!(config.host_name, "com.securepassword.host");
        assert!(!config.allowed_origins.is_empty());
        assert!(!config.allowed_extensions.is_empty());
        assert_eq!(config.protocol_version, ProtocolVersion::V2);
        assert_eq!(config.max_concurrent_requests, 100);
    }

    #[test]
    fn test_host_stats_default() {
        let stats = HostStats::default();
        
        assert!(stats.started_at.is_none());
        assert_eq!(stats.total_requests, 0);
        assert_eq!(stats.successful_requests, 0);
        assert_eq!(stats.failed_requests, 0);
        assert_eq!(stats.concurrent_requests, 0);
        assert_eq!(stats.avg_response_time_ms, 0.0);
        assert!(!stats.ipc_connected);
    }

    #[tokio::test]
    async fn test_message_validation() {
        let config = HostConfig::default();
        let host = NativeMessagingHost::new(config).await.unwrap();
        
        // 有效消息
        let valid_message = NativeMessage::new_request(
            "test-id".to_string(),
            serde_json::json!({"action": "test"}),
            "test-extension".to_string(),
        );
        
        assert!(host.validate_browser_message(&valid_message).await.is_ok());
        
        // 无效消息 (空请求ID)
        let mut invalid_message = valid_message.clone();
        invalid_message.request_id = "".to_string();
        
        assert!(host.validate_browser_message(&invalid_message).await.is_err());
    }

    #[tokio::test]
    async fn test_ping_message_handling() {
        let config = HostConfig::default();
        let host = NativeMessagingHost::new(config).await.unwrap();
        
        let ping_message = NativeMessage::new_ping("test-extension".to_string());
        let response = host.handle_ping_message(&ping_message).await.unwrap();
        
        assert_eq!(response.get_message_type(), MessageType::Pong);
        assert_eq!(response.request_id, ping_message.request_id);
    }

    #[tokio::test]
    async fn test_stats_updates() {
        let config = HostConfig::default();
        let host = NativeMessagingHost::new(config).await.unwrap();
        
        // 测试统计信息更新
        host.increment_total_requests().await;
        host.increment_successful_requests().await;
        host.increment_concurrent_requests().await;
        
        let stats = host.get_stats().await;
        assert_eq!(stats.total_requests, 1);
        assert_eq!(stats.successful_requests, 1);
        assert_eq!(stats.concurrent_requests, 1);
        
        host.decrement_concurrent_requests().await;
        let stats = host.get_stats().await;
        assert_eq!(stats.concurrent_requests, 0);
    }
} 