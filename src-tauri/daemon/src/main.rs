//! 企业级独立守护进程主入口
//! 
//! 提供7x24小时稳定运行的系统服务，负责：
//! - Native Messaging 代理
//! - IPC 通信服务
//! - Tauri 应用管理
//! - 企业级安全防护

use tracing::{info, error};
use anyhow::Result;

mod daemon_core;
mod config;
mod platform;
mod ipc;
mod native_messaging;
mod app_manager;
mod security;
mod monitoring;
mod error;
mod utils;

use daemon_core::SecurePasswordDaemon;
use config::DaemonConfig;
use utils::logging::init_logging;
use native_messaging::{NativeMessagingHost, HostConfig};
use ipc::ClientConfig as IpcClientConfig;

#[tokio::main]
async fn main() -> Result<()> {
    // 解析命令行参数
    let args = parse_command_line_args();

    // 如果是Native Messaging模式，直接启动Native Messaging Host
    // 注意：Native Messaging模式下不能初始化日志到stdout，会污染协议通信
    if args.native_messaging_mode {
        return run_native_messaging_host(&args).await;
    }

    // 初始化日志系统 (仅在非Native Messaging模式下)
    init_logging()?;

    info!("🚀 启动 Secure Password 守护进程 v{}", env!("CARGO_PKG_VERSION"));

    // 自动检测Native Messaging模式：
    // 如果没有命令行参数且stdin可用，可能是Chrome调用的Native Messaging Host
    if should_auto_detect_native_messaging(&args) {
        eprintln!("🔍 检测到可能的Native Messaging调用，尝试自动切换模式");
        if let Some(timeout_result) = detect_native_messaging_input().await {
            if timeout_result {
                eprintln!("✅ 检测到Native Messaging输入，切换到Native Messaging Host模式");
                return run_native_messaging_host(&args).await;
            } else {
                eprintln!("⏰ 未在超时时间内检测到Native Messaging输入，继续正常启动");
            }
        }
    }

    // 加载配置文件
    let config = DaemonConfig::load_from_file(&args.config_path)
        .await
        .map_err(|e| {
            error!("配置文件加载失败: {}", e);
            e
        })?;

    // 验证配置
    config.validate()
        .map_err(|e| {
            error!("配置验证失败: {}", e);
            e
        })?;

    // 创建守护进程实例
    let mut daemon = SecurePasswordDaemon::new(config.clone())
        .await
        .map_err(|e| {
            error!("守护进程创建失败: {}", e);
            e
        })?;

    // 设置信号处理
    let shutdown_signal = setup_signal_handlers();

    // 启动守护进程
    match daemon.start().await {
        Ok(()) => {
            info!("✅ 守护进程启动成功");
            
            // 等待关闭信号
            shutdown_signal.await;
            
            info!("📥 收到关闭信号，开始优雅关闭...");
            
            // 优雅关闭
            if let Err(e) = daemon.shutdown().await {
                error!("守护进程关闭失败: {}", e);
                std::process::exit(1);
            }
            
            info!("✅ 守护进程已安全关闭");
        }
        Err(e) => {
            error!("❌ 守护进程启动失败: {}", e);
            std::process::exit(1);
        }
    }
    
    Ok(())
}

/// 判断是否应该自动检测Native Messaging模式
fn should_auto_detect_native_messaging(args: &CommandLineArgs) -> bool {
    // 如果没有显式指定daemon模式且没有指定native-messaging模式
    // 且使用默认配置路径，则可能是Chrome调用
    !args.daemon_mode && !args.native_messaging_mode && args.config_path == "daemon.toml"
}

/// 检测是否有Native Messaging输入
/// 返回Some(true)如果检测到输入，Some(false)如果超时，None如果检测失败
async fn detect_native_messaging_input() -> Option<bool> {
    // 首先检查stdin是否是终端
    // 如果是终端，说明是用户交互模式，不是Native Messaging
    if atty::is(atty::Stream::Stdin) {
        return Some(false);
    }

    // 检查环境变量，Chrome可能会设置特定的环境变量
    if std::env::var("CHROME_NATIVE_MESSAGING").is_ok() {
        return Some(true);
    }

    // 检查父进程名称，看是否是浏览器调用
    if let Ok(parent_name) = get_parent_process_name() {
        let browser_names = ["chrome", "chromium", "firefox", "edge", "safari"];
        if browser_names.iter().any(|&name| parent_name.to_lowercase().contains(name)) {
            return Some(true);
        }
    }

    // 如果stdin不是终端且没有其他明确指示，假设可能是Native Messaging
    // 但这是一个保守的假设
    Some(true)
}

/// 获取父进程名称
fn get_parent_process_name() -> Result<String, Box<dyn std::error::Error>> {
    use std::process::Command;

    // 获取父进程ID
    let ppid = unsafe { libc::getppid() };

    // 使用ps命令获取进程名称
    let output = Command::new("ps")
        .args(&["-p", &ppid.to_string(), "-o", "comm="])
        .output()?;

    if output.status.success() {
        let name = String::from_utf8_lossy(&output.stdout).trim().to_string();
        Ok(name)
    } else {
        Err("Failed to get parent process name".into())
    }
}

/// 运行Native Messaging Host模式
async fn run_native_messaging_host(_args: &CommandLineArgs) -> Result<()> {
    // 在Native Messaging模式下，不初始化标准日志，避免污染stdout
    // stdout必须保留给Native Messaging协议使用
    eprintln!("🔗 启动 Native Messaging Host 模式");

    // 创建Host配置
    let host_config = HostConfig {
        host_name: "com.secure_password.native_messaging_host".to_string(),
        description: "Secure Password Native Messaging Host".to_string(),
        executable_path: std::env::current_exe()
            .unwrap_or_else(|_| "secure-password-daemon".into())
            .to_string_lossy()
            .to_string(),
        allowed_origins: vec![
            "chrome-extension://laiidmaciomhdbdinfaegennlkbdecjp/".to_string(),
        ],
        allowed_extensions: vec![
            "laiidmaciomhdbdinfaegennlkbdecjp".to_string(),
        ],
        ipc_config: IpcClientConfig {
            server_address: "127.0.0.1".to_string(),
            port: Some(8080),
            timeout_ms: 30000,
            request_timeout_ms: 10000,
            reconnect_interval_ms: 1000,
            max_reconnect_attempts: 3,
            heartbeat_interval_ms: 30000,
            auto_reconnect: true,
            transport_type: crate::ipc::transport::TransportType::Tcp,
            message_buffer_size: 8192,
        },
        protocol_version: crate::native_messaging::protocol::ProtocolVersion::V2,
        request_timeout: std::time::Duration::from_secs(30),
        max_concurrent_requests: 100,
        debug_mode: true,
        heartbeat_interval: std::time::Duration::from_secs(30),
        verify_signatures: false,
        signature_key: None,
    };

    eprintln!("📋 Native Messaging Host 配置完成");

    // 创建并启动Native Messaging Host
    let mut host = NativeMessagingHost::new(host_config)
        .await
        .map_err(|e| {
            eprintln!("❌ 创建Native Messaging Host失败: {}", e);
            anyhow::anyhow!("创建Native Messaging Host失败: {}", e)
        })?;

    eprintln!("🚀 Native Messaging Host 创建成功，开始启动...");

    // 启动Host（这会阻塞直到Host关闭）
    match host.start().await {
        Ok(()) => {
            eprintln!("✅ Native Messaging Host 正常关闭");
        }
        Err(e) => {
            eprintln!("❌ Native Messaging Host运行失败: {}", e);
            return Err(anyhow::anyhow!("Native Messaging Host运行失败: {}", e));
        }
    }

    eprintln!("🔚 Native Messaging Host 已关闭");
    Ok(())
}

/// 命令行参数结构
#[derive(Debug)]
struct CommandLineArgs {
    config_path: String,
    log_level: String,
    daemon_mode: bool,
    native_messaging_mode: bool,
}

/// 解析命令行参数
fn parse_command_line_args() -> CommandLineArgs {
    use clap::{Arg, Command};
    
    let matches = Command::new("secure-password-daemon")
        .version(env!("CARGO_PKG_VERSION"))
        .about("企业级密码管理守护进程")
        .arg(
            Arg::new("config")
                .short('c')
                .long("config")
                .value_name("FILE")
                .help("配置文件路径")
                .default_value("daemon.toml")
        )
        .arg(
            Arg::new("log-level")
                .short('l')
                .long("log-level")
                .value_name("LEVEL")
                .help("日志级别 (trace, debug, info, warn, error)")
                .default_value("info")
        )
        .arg(
            Arg::new("daemon")
                .short('d')
                .long("daemon")
                .help("以守护进程模式运行")
                .action(clap::ArgAction::SetTrue)
        )
        .arg(
            Arg::new("native-messaging")
                .short('n')
                .long("native-messaging")
                .help("以Native Messaging Host模式运行")
                .action(clap::ArgAction::SetTrue)
        )
        .get_matches();

    CommandLineArgs {
        config_path: matches.get_one::<String>("config").unwrap().clone(),
        log_level: matches.get_one::<String>("log-level").unwrap().clone(),
        daemon_mode: matches.get_flag("daemon"),
        native_messaging_mode: matches.get_flag("native-messaging"),
    }
}

/// 设置信号处理器
async fn setup_signal_handlers() {
    #[cfg(unix)]
    {
        use tokio::signal::unix::{signal, SignalKind};

        let mut sigterm = signal(SignalKind::terminate()).unwrap();
        let mut sigint = signal(SignalKind::interrupt()).unwrap();
        let mut sighup = signal(SignalKind::hangup()).unwrap();

        tokio::select! {
            _ = sigterm.recv() => {
                info!("收到 SIGTERM 信号");
            }
            _ = sigint.recv() => {
                info!("收到 SIGINT 信号");
            }
            _ = sighup.recv() => {
                info!("收到 SIGHUP 信号");
            }
        }
    }

    #[cfg(windows)]
    {
        tokio::signal::ctrl_c().await.expect("无法设置 Ctrl+C 处理器");
        info!("收到 Ctrl+C 信号");
    }
}
